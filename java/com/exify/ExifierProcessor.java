package com.exify;

public class ExifierProcessor implements AutoCloseable {
    // Load the native library
    static {
        System.loadLibrary("exify");
    }
    
    // Native method declarations
    private static native long createExifier();
    private static native void destroyExifier(long handle);
    private static native void loadGrammar(long handle, String grammarFilePath);
    private static native byte[] encode(long handle, String jsonData);
    private static native String decode(long handle, byte[] encodedBytes);
    private static native String getVersion(long handle);
    
    private long handle;
    
    /**
     * Create a new ExifierProcessor instance
     */
    public ExifierProcessor() {
        this.handle = createExifier();
        if (this.handle == 0) {
            throw new RuntimeException("Failed to create Exifier instance");
        }
    }
    
    /**
     * Load grammar from a file path
     */
    public void loadGrammar(String grammarFilePath) throws Exception {
        loadGrammar(this.handle, grammarFilePath);
    }
    
    /**
     * Encode a JSON string to bytes
     */
    public byte[] encode(String jsonData) throws Exception {
        return encode(this.handle, jsonData);
    }
    
    /**
     * Decode bytes to a JSON string
     */
    public String decode(byte[] encodedBytes) throws Exception {
        return decode(this.handle, encodedBytes);
    }

    /**
     * Returns version
     */
    public String getVersion() throws Exception {
        return getVersion(this.handle);
    }
    
    /**
     * Clean up native resources
     */
    @Override
    public void close() {
        if (this.handle != 0) {
            destroyExifier(this.handle);
            this.handle = 0;
        }
    }
    
    public static void main(String[] args) {
        try (ExifierProcessor processor = new ExifierProcessor()) {
            // Load grammar (replace with your actual grammar file path)
            // processor.loadGrammar("path/to/grammar.xsd");
            
            // Log version
            System.out.println("Codec version: " + processor.getVersion());

            // Test encoding
            String testJson = "{\"name\":\"test\",\"value\":123}";
            System.out.println("Original JSON: " + testJson);
            
            byte[] encoded = processor.encode(testJson);
            System.out.println("Encoded bytes length: " + encoded.length);
            
            // Test decoding
            String decoded = processor.decode(encoded);
            System.out.println("Decoded JSON: " + decoded);
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}