use std::fs;
use std::path::PathBuf;
use std::time::Instant;

use base64::{engine::general_purpose, Engine};
use clap::Parser;
use serde_json::Value;
// To be removed later.
use tracing_subscriber;
mod exifier;
pub use exifier::convert_to_xml_schema;

use exifier::{EXIGrammar, Error, Exifier};

/// Compare a JSON string with a JSON value semantically, ignoring element order
fn compare_json_string_with_value(json_str: &str, json_value: &Value) -> Result<bool, String> {
    let input_json: Value = serde_json::from_str(json_str).unwrap();
    // Compare the parsed values
    Ok(&input_json == json_value)
}

#[derive(Parser, Debug)]
#[command(version, about, long_about = None)]
struct Args {
    /// Name of the person to greet
    #[arg(
        short,
        long,
        help = "Json to encode to exi",
        default_value = r#"{"lastname": "<PERSON>","age": 30}"#
    )]
    json: String,
    #[arg(
        short,
        long,
        help = "Path to grammar file",
        default_value = r#"resources/examples/example_1/test_schema.xsd.grs"#
    )]
    grammar: String,
    #[arg(short, long, help = "Exi to decode", default_value = r#""#)]
    exi: String,

    #[arg(short = 'w', long, help = "Write exi to file", default_value_t = false)]
    w: bool,

    #[arg(
        short = 'c',
        long,
        help = "Convert json schema to xml schema",
        default_value = ""
    )]
    convert: String,
}

fn main() -> Result<(), Error> {
    tracing_subscriber::fmt::init();
    let args = Args::parse();
    let json_to_encode = args.json;

    if args.convert.len() > 0 {
        tracing::info!("Converting json schema to xml schema");
        let json_schema = fs::read_to_string(args.convert).expect("Error reading json schema");
        let xsd = convert_to_xml_schema(&json_schema).expect("Conversion should succeed");
        tracing::debug!("{xsd}");
        return Ok(());
    }

    tracing::info!(
        "Json to encode: {}",
        serde_json::to_string_pretty(&json_to_encode).unwrap()
    );
    let base_64_encoded = general_purpose::STANDARD.encode(&json_to_encode);

    // Read grammar content from file path
    let grammar_path = args.grammar;

    let grammar_content = match fs::read_to_string(&grammar_path) {
        Ok(content) => content,
        Err(e) => {
            tracing::error!(
                "Error reading grammar file from path: {} (Path provided: {grammar_path})",
                e
            );
            return Err(Error::IO(e));
        }
    };

    tracing::debug!("Grammar content loaded from file.");

    // Parse the GRS file
    let exi_grammar = match EXIGrammar::from_json(&grammar_content) {
        Ok(grammar) => grammar,
        Err(e) => {
            tracing::error!("Error parsing GRS file: {}", e);
            return Err(Error::EXILoadingGrammarError(format!(
                "Failed to parse GRS file: {}",
                e
            )));
        }
    };

    tracing::debug!("GRS file read.");
    tracing::debug!(
        "Document grammar ID: {}",
        exi_grammar.grs.document_grammar_id
    );
    tracing::debug!(
        "Fragment grammar ID: {}",
        exi_grammar.grs.fragment_grammar_id
    );

    let mut exifier = Exifier::default();
    match exifier.load_grammar(exi_grammar) {
        Ok(_) => tracing::debug!("Grammar loaded successfully."),
        Err(e) => {
            tracing::error!("Failed to load grammar: {}", e);
            return Err(e);
        }
    }

    // Parse JSON string to Value for encoding
    let json_value: Value = match serde_json::from_str(&json_to_encode) {
        Ok(val) => val,
        Err(e) => {
            tracing::error!("Failed to parse JSON string: {}", e);
            return Err(Error::ErrorParsingInputJSON(format!(
                "(Error while parsing JSON: {}). Input: {} ",
                e, &json_to_encode
            )));
        }
    };

    let start_time = Instant::now();
    let encoded_bytes = match exifier.encode(json_value) {
        Ok(bytes) => bytes,
        Err(e) => {
            tracing::error!("Failed to encode JSON: {}", e);
            return Err(e);
        }
    };
    let duration = start_time.elapsed();
    tracing::info!("Encoding took: {:?}", duration);
    tracing::info!("Encoded exi:");
    let hex = encoded_bytes
        .iter()
        .map(|b| format!("{:02X}", b))
        .collect::<Vec<_>>()
        .join(" ");
    tracing::info!("{}", hex);

    if args.w {
        let exi_path = PathBuf::from("./output.exi");
        fs::write(exi_path, &encoded_bytes).expect("Failed to write exi to file");
        tracing::info!("Wrote exi to file: {}", "./output.exi");
    }

    let start_time = Instant::now();
    let decoded_json = exifier.decode(encoded_bytes.clone()).unwrap();
    let duration = start_time.elapsed();
    tracing::info!("Decoding took: {:?}", duration);
    tracing::info!("Decoded json: {decoded_json}");

    // Compare the original and decoded JSON
    let comparison_result = compare_json_string_with_value(&json_to_encode, &decoded_json);
    match comparison_result {
        Ok(are_equal) => {
            if are_equal {
                tracing::info!(
                    "✅ JSON comparison: Original and decoded JSON are semantically identical"
                );
            } else {
                tracing::warn!("❌ JSON comparison: Original and decoded JSON are different\n Original: {json_to_encode}\n Decoded: {decoded_json}");
            }
        }
        Err(e) => {
            tracing::error!("❌ JSON comparison failed: {}", e);
        }
    }
    tracing::info!(
        "Size: {} bytes\nBase64 encoded size: {}\nCompression: {}%",
        &encoded_bytes.len(),
        base_64_encoded.as_bytes().len(),
        (100.0 * (base_64_encoded.as_bytes().len() as f32 - encoded_bytes.len() as f32)
            / base_64_encoded.as_bytes().len() as f32)
            .round()
    );
    Ok(())
}
