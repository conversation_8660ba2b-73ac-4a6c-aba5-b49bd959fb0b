#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Generic {0}")]
    Generic(String),
    #[error("Couldn't parse input JSON: {0}")]
    ErrorParsingInputJSON(String),
    #[error("EXI Encoding Error: {0}")]
    EXIEncodingError(String),
    #[error("Error while loading grammar: {0}")]
    EXILoadingGrammarError(String),
    #[error("Error initialising EXI encoder: {0}")]
    EXIEncoderInitError(String),
    #[error(transparent)]
    IO(#[from] std::io::Error),
}

impl PartialEq for Error {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (Error::Generic(a), Error::Generic(b)) => a == b,
            (Error::ErrorParsingInputJSON(a), Error::ErrorParsingInputJSON(b)) => a == b,
            (Error::EXIEncodingError(a), Error::EXIEncodingError(b)) => a == b,
            (Error::EXILoadingGrammarError(a), Error::EXILoadingGrammarError(b)) => a == b,
            (Error::EXIEncoderInitError(a), Error::EXIEncoderInitError(b)) => a == b,
            // For IO errors, we compare the error kind and message
            (Error::IO(a), Error::IO(b)) => a.kind() == b.kind() && a.to_string() == b.to_string(),
            _ => false,
        }
    }
}
