use crate::exifier::grammar::{EXIGrammar, Production};

use crate::exifier::prelude::*;
use crate::exifier::writer::BitStreamWriter;
use quick_xml::events::{BytesEnd, BytesStart, BytesText, Event};
use quick_xml::reader::Reader;
use serde_json::Value;
use xml2json_rs::XmlConfig;

#[derive(Debug)]
pub struct ExiEncoder {
    grammar: EXIGrammar,
    xml: String,
    bitstream_writer: BitStreamWriter,
    current_grammar_id: i32,
    grammar_stack: Vec<Production>,
}

impl ExiEncoder {
    pub fn new() -> Self {
        ExiEncoder {
            grammar: EXIGrammar::default(),
            xml: String::new(),
            bitstream_writer: BitStreamWriter::new(),
            current_grammar_id: 0,
            grammar_stack: Vec::new(),
        }
    }

    pub fn load_grammar(&mut self, exi_grammar: EXIGrammar) -> Result<()> {
        tracing::debug!(
            "Loading EXI grammar with {} grammar rules",
            exi_grammar.grs.grammar.len()
        );
        tracing::debug!(
            "Document grammar ID: {}",
            exi_grammar.grs.document_grammar_id
        );
        tracing::debug!(
            "Fragment grammar ID: {}",
            exi_grammar.grs.fragment_grammar_id
        );
        // TODO: Store the grammar for use in encoding/decoding
        self.grammar = exi_grammar;
        Ok(())
    }

    pub fn encode(&mut self, json_value: Value) -> Result<Vec<u8>> {
        tracing::debug!("Encode JSON value: {}", json_value);

        // Convert JSON value to string for XML conversion
        let json_string = serde_json::to_string(&json_value).unwrap();
        self.prepare_encoder(&json_string)?;
        self.encode_header()?;
        self.encode_body()?;

        let exi_bytes = self.bitstream_writer.into_bytes();
        let hex = exi_bytes
            .iter()
            .map(|b| format!("{:02X}", b))
            .collect::<Vec<_>>()
            .join(" ");

        tracing::debug!("Encoded EXI bytes: {:?}", hex);

        // Return the incoming JSON as a vector of bytes
        Ok(exi_bytes)
    }

    fn parse_xml(&mut self) {
        // Clone the XML string to avoid borrowing issues
        let xml_content = self.xml.clone();
        let mut reader = Reader::from_str(&xml_content);
        self.current_grammar_id = 1;
        let mut buf = Vec::new();
        loop {
            tracing::trace!("Current grammar id: {:?}", self.current_grammar_id);
            //tracing::debug!("Current grammar stack: {:?}", self.grammar_stack);
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(e)) => {
                    tracing::debug!(
                        "Start element: {:?} [Current grammar: {}] [Grammar stack: {:?}]",
                        e.name(),
                        self.current_grammar_id,
                        self.grammar_stack
                    );
                    let _ = self.encode_event(&Event::Start(e));
                }
                Ok(Event::Text(e)) => {
                    tracing::debug!(
                        "Text: {:?} [Current grammar: {}] [Grammar stack: {:?}]",
                        e.unescape().unwrap(),
                        self.current_grammar_id,
                        self.grammar_stack
                    );
                    let _ = self.encode_event(&Event::Text(e));
                }
                Ok(Event::End(e)) => {
                    tracing::debug!(
                        "End element: {:?} [Current grammar: {}] [Grammar stack: {:?}]",
                        e.name(),
                        self.current_grammar_id,
                        self.grammar_stack
                    );
                    let _ = self.encode_event(&Event::End(e));
                    let next_rule = self.grammar_stack.pop();
                    if let Some(rule) = next_rule {
                        self.current_grammar_id = rule.next_grammar_id.unwrap();
                    }
                }
                Ok(Event::Eof) => {
                    tracing::debug!(
                        "End of file [Current grammar: {}] [Grammar stack: {:?}]",
                        self.current_grammar_id,
                        self.grammar_stack
                    );
                    break;
                }
                Err(e) => panic!(
                    "Error: {:?} [Current grammar: {}] [Grammar stack: {:?}]",
                    e, self.current_grammar_id, self.grammar_stack
                ),
                Ok(Event::Empty(e)) => {
                    tracing::debug!(
                        "Empty element: {:?} [Current grammar: {}]",
                        e.name(),
                        self.current_grammar_id
                    );
                    // let name = e.name();
                    // let _ = self.encode_event(&Event::Start(BytesStart::from(name)));
                    // let _ = self.encode_event(&Event::Text(BytesText::from_escaped("")));
                    // let _ = self.encode_event(&Event::End(BytesEnd::from(name)));
                }
                Ok(Event::Decl(e)) => {
                    tracing::trace!(
                        "Declaration. {:#?} [Current grammar: {}]",
                        e,
                        self.current_grammar_id
                    );
                }
                _ => {
                    tracing::error!("Unsupported event: {:?}", buf);
                }
            }
            buf.clear();
        }

        tracing::debug!(
            "Parse completed. Final grammar stack: {:?}",
            self.grammar_stack
        );
    }

    fn encode_event(&mut self, event: &Event) -> Result<()> {
        match event {
            Event::Start(e) => {
                let current_rule = self
                    .grammar
                    .grs
                    .grammar
                    .iter()
                    .find(|g| g.grammar_id == self.current_grammar_id.to_string());
                // let matching_production = current_rule.production.iter().find(|p| p.event == "startElement" && self.grammar.qnames.namespace_context.get(p.start_element_namespace_id.unwrap()).qnames_context.find(|q| q.local_name_id == p.start_element_local_name_id.unwrap()).is_some());

                if let Some(rule) = current_rule {
                    for (index, production) in rule.production.iter().enumerate() {
                        if production.event == "startElement" {
                            if let Some(namespace_id) = production.start_element_namespace_id {
                                if let Some(namespace) = self
                                    .grammar
                                    .qnames
                                    .namespace_context
                                    .get(namespace_id as usize)
                                {
                                    if let Some(local_name_id) =
                                        production.start_element_local_name_id
                                    {
                                        if let Some(local_name) = namespace
                                            .qname_context
                                            .iter()
                                            .find(|q| q.local_name_id == local_name_id)
                                        {
                                            if local_name.local_name.as_bytes() == e.name().as_ref()
                                            {
                                                self.current_grammar_id =
                                                    production.start_element_grammar_id.unwrap()
                                                        as i32;
                                                tracing::trace!(
                                                    "Matched start element: {}. Index: {}/{}",
                                                    local_name.local_name,
                                                    index,
                                                    rule.production.len()
                                                );
                                                let bits_to_use =
                                                    (rule.production.len() as f32).log2().ceil()
                                                        as u8;
                                                self.bitstream_writer
                                                    .write_n_bit_data(index as u8, bits_to_use);
                                                self.grammar_stack.push(production.clone());
                                                return Ok(());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Event::Text(e) => {
                // First, collect the information we need while borrowing immutably
                let (element_type_opt, element_data_type) = {
                    let current_rule = self
                        .grammar
                        .grs
                        .grammar
                        .iter()
                        .find(|g| g.grammar_id == self.current_grammar_id.to_string());

                    tracing::trace!("Current rule: {:?}", current_rule);
                    let mut found_element_type = None;
                    let mut element_data_type: String = String::new();

                    if let Some(rule) = current_rule {
                        for (index, production) in rule.production.iter().enumerate() {
                            if &production.event == "startElement" {
                                let qname_context = self.grammar.qnames.namespace_context
                                    [production.start_element_namespace_id.unwrap() as usize]
                                    .qname_context
                                    .iter()
                                    .find(|n| {
                                        n.uri_id == production.start_element_namespace_id.unwrap()
                                            && n.local_name_id
                                                == production.start_element_local_name_id.unwrap()
                                    })
                                    .unwrap();

                                let bits_to_use =
                                    (rule.production.len() as f32).log2().ceil() as u8;
                                // bits_to_use = bits_to_use.max(1);
                                self.bitstream_writer
                                    .write_n_bit_data(index as u8, bits_to_use);
                                found_element_type = Some(qname_context.local_name.clone());
                                self.current_grammar_id = production.next_grammar_id.unwrap();

                                let prod_type_definition = self
                                    .grammar
                                    .grs
                                    .grammar
                                    .iter()
                                    .find(|g| {
                                        g.grammar_id
                                            == production
                                                .start_element_grammar_id
                                                .unwrap()
                                                .to_string()
                                    })
                                    .unwrap();
                                if prod_type_definition.production.len() == 1
                                    && prod_type_definition.production[0].event == "characters"
                                {
                                    element_data_type = self
                                        .grammar
                                        .simple_datatypes
                                        .iter()
                                        .find(|dt| {
                                            dt.simple_datatype_id
                                                == prod_type_definition.production[0]
                                                    .characters_datatype_id
                                                    .unwrap()
                                        })
                                        .unwrap()
                                        .datatype
                                        .clone();
                                } else {
                                    tracing::error!("Multiple productions for element: {:?}. This is unexpected.", prod_type_definition);
                                }
                                break;
                            }
                        }
                    }

                    (found_element_type, element_data_type)
                };

                // Now we can use mutable borrows since the immutable borrow is done
                if let Some(element_type) = element_type_opt {
                    tracing::trace!("Element type is {:#?}", element_type);

                    if element_type == "string" {
                        // Write the text
                        let text = e.unescape().unwrap();
                        tracing::debug!("Writing text: {}", text);
                        let prefix_increment = 2;
                        let length_to_write =
                            text.as_bytes().len() as u128 + prefix_increment as u128;
                        self.write_unsigned_int(length_to_write);
                        for byte in text.as_bytes() {
                            self.bitstream_writer.write_n_bit_data(*byte, 8);
                        }
                    } else if element_type == "number" {
                        let text = e.unescape().unwrap();
                        tracing::trace!(
                            "Writing number: {} (Datatype: {})",
                            text,
                            element_data_type
                        );

                        if element_data_type == "FLOAT" {
                            let number_to_write = text.parse::<f64>().unwrap();
                            tracing::trace!("Number to write: {}", number_to_write);
                            let (sign, mantissa, exponent) =
                                self.extract_f64_parts(number_to_write);
                            tracing::trace!(
                                "Mantissa: {}, Exponent: {}, Sign: {}",
                                mantissa,
                                exponent,
                                sign
                            );
                            self.write_number(mantissa as i128 * sign as i128);
                            self.write_number(exponent as i128);
                        }
                        if element_data_type == "DOUBLE" {
                            let int_val: i128 = text.parse::<i128>().unwrap_or(0);
                            self.write_number(int_val);
                        }
                    }
                }
            }
            Event::End(_e) => {
                let current_rule = self
                    .grammar
                    .grs
                    .grammar
                    .iter()
                    .find(|g| g.grammar_id == self.current_grammar_id.to_string());

                if let Some(rule) = current_rule {
                    for (index, production) in rule.production.iter().enumerate() {
                        if production.event == "endElement" {
                            tracing::trace!(
                                "Matched end element. Index: {}/{}",
                                index,
                                rule.production.len()
                            );
                            let bits_to_use = (rule.production.len() as f32).log2().ceil() as u8;
                            self.bitstream_writer
                                .write_n_bit_data(index as u8, bits_to_use);
                            return Ok(());
                        }
                    }
                    tracing::error!("Did not find end element production in rule: {:?}", rule);
                }
            }
            _ => {}
        }
        Ok(())
    }

    fn write_number(&mut self, value: i128) {
        let mut value_to_write = value.abs();
        if value >= 0 {
            self.bitstream_writer.write_n_bit_data(0, 1);
        } else {
            self.bitstream_writer.write_n_bit_data(1, 1);
            value_to_write = value_to_write - 1;
        }
        self.write_unsigned_int(value_to_write as u128);
    }

    fn write_unsigned_int(&mut self, value: u128) {
        let mut data = value;

        while data >= 128 {
            self.bitstream_writer
                .write_n_bit_data(((data & 0x7F) | 0x80) as u8, 8);
            data >>= 7;
        }

        self.bitstream_writer.write_n_bit_data(data as u8, 8);
    }

    fn prepare_encoder(&mut self, json_string: &str) -> Result<()> {
        let mut xml_builder = XmlConfig::new().root_name(String::from("map")).finalize();
        self.xml = match xml_builder.build_from_json_string(&json_string) {
            Ok(xml) => xml,
            Err(e) => {
                return Err(crate::exifier::Error::EXIEncoderInitError(format!(
                    "Error initialising EXI encoder: {}",
                    e
                )));
            }
        };
        tracing::debug!("Generated XML: {}", &self.xml);
        Ok(())
    }

    fn encode_header(&mut self) -> Result<()> {
        self.bitstream_writer.write_n_bit_data(2, 2);
        // // Options bit 0 (1 bit)
        self.bitstream_writer.write_n_bit_data(0, 1);
        // // Header version 00000 (5 bits)
        self.bitstream_writer.write_n_bit_data(0, 5);

        tracing::debug!("Encoded header.");
        Ok(())
    }

    fn encode_body(&mut self) -> Result<()> {
        self.parse_xml();
        Ok(())
    }

    fn extract_f64_parts(&self, x: f64) -> (i32, u64, i16) {
        let bits = x.to_bits(); // Get the raw bits of the f64

        let sign = if (bits >> 63) == 0 { 1 } else { -1 }; // 1 if positive, -1 if negative
        let exponent_bits = ((bits >> 52) & 0x7FF) as u16; // Extract bits 52-62
        let mantissa = bits & 0xFFFFFFFFFFFFF; // Extract bits 0-51

        let exponent = if exponent_bits == 0 {
            // Denormalized number (subnormal)
            -1022
        } else {
            // Normalized number
            (exponent_bits as i16) - 1023
        };

        (sign, mantissa, exponent)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_write_number_127() {
        let mut encoder = ExiEncoder::new();
        encoder.write_number(127);
        assert_eq!(
            encoder.bitstream_writer.into_bytes(),
            vec![0b00111111, 0b10000000]
        );
    }

    #[test]
    fn test_write_number_128() {
        let mut encoder = ExiEncoder::new();
        encoder.write_number(128);
        assert_eq!(
            encoder.bitstream_writer.into_bytes(),
            vec![0b01000000, 0b00000000, 0b10000000]
        );
    }

    #[test]
    fn test_write_number_256() {
        let mut encoder = ExiEncoder::new();
        encoder.write_number(256);
        assert_eq!(
            encoder.bitstream_writer.into_bytes(),
            vec![0b01000000, 0b00000001, 0b00000000]
        );
    }

    #[test]
    fn test_write_unsigned_int_127() {
        let mut encoder = ExiEncoder::new();
        encoder.write_unsigned_int(127);
        assert_eq!(encoder.bitstream_writer.into_bytes(), vec![0b01111111]);
    }

    #[test]
    fn test_write_unsigned_int_128() {
        let mut encoder = ExiEncoder::new();
        encoder.write_unsigned_int(128);
        assert_eq!(
            encoder.bitstream_writer.into_bytes(),
            vec![0b10000000, 0b00000001]
        );
    }

    #[test]
    fn test_write_unsigned_int_256() {
        let mut encoder = ExiEncoder::new();
        encoder.write_unsigned_int(256);
        assert_eq!(
            encoder.bitstream_writer.into_bytes(),
            vec![0b10000000, 0b00000010]
        );
    }

    #[test]
    fn test_write_unsigned_int_512() {
        let mut encoder = ExiEncoder::new();
        encoder.write_unsigned_int(32768);
        assert_eq!(
            encoder.bitstream_writer.into_bytes(),
            vec![0b10000000, 0b10000000, 0b00000010]
        );
    }
}
