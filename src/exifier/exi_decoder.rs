use crate::exifier::grammar::{EXIGrammar, Production};
use crate::exifier::prelude::*;
use crate::exifier::reader::BitStreamReader;
use serde_json::{Map, Number, Value};
use std::str;

#[derive(Debug)]
pub struct ExiDecoder {
    grammar: EXIGrammar,
    json_object: Map<String, Value>,
    bitstream_reader: BitStreamReader,
}

impl ExiDecoder {
    pub fn new() -> Self {
        ExiDecoder {
            grammar: EXIGrammar::default(),
            json_object: Map::new(),
            bitstream_reader: BitStreamReader::default(),
        }
    }

    pub fn load_grammar(&mut self, exi_grammar: EXIGrammar) -> Result<()> {
        tracing::debug!(
            "Loading EXI grammar with {} grammar rules",
            exi_grammar.grs.grammar.len()
        );
        tracing::debug!(
            "Document grammar ID: {}",
            exi_grammar.grs.document_grammar_id
        );
        tracing::debug!(
            "Fragment grammar ID: {}",
            exi_grammar.grs.fragment_grammar_id
        );
        // TODO: Store the grammar for use in encoding/decoding
        self.grammar = exi_grammar;
        Ok(())
    }

    pub fn decode(&mut self, encoded_bytes: Vec<u8>) -> Result<Value> {
        // Decode the incoming bytes to a string
        self.bitstream_reader = BitStreamReader::new(encoded_bytes.clone());

        self.decode_header()?; // This is where document grammar begins.
        self.decode_body()?;

        tracing::debug!("Decoded bytes to JSON string: {:#?}", self.json_object);

        if self.json_object.is_empty() {
            return Err(crate::exifier::Error::Generic(format!(
                "Failed to decode JSON"
            )));
        }
        Ok(Value::Object(self.json_object.clone()))
    }

    fn decode_header(&mut self) -> Result<()> {
        let first_two_bits = self.bitstream_reader.read_n_bit_data(2)?;
        if first_two_bits != 2 {
            return Err(crate::exifier::Error::Generic(format!(
                "Invalid header. Expected 2, got {}",
                first_two_bits
            )));
        }

        let options_bit = self.bitstream_reader.read_n_bit_data(1)?;
        if options_bit != 0 {
            return Err(crate::exifier::Error::Generic(format!(
                "Invalid header. Expected 0, got {}",
                options_bit
            )));
        }

        let header_version = self.bitstream_reader.read_n_bit_data(5)?;
        if header_version != 0 {
            return Err(crate::exifier::Error::Generic(format!(
                "Invalid header. Expected 0, got {}",
                header_version
            )));
        }

        tracing::debug!(
            "Decoded header. Options: {}, Header version: {}",
            options_bit,
            header_version
        );
        Ok(())
    }

    fn decode_body(&mut self) -> Result<()> {
        let mut opened_elements: Vec<String> = Vec::new();
        let mut current_grammar_id: i32 = 1;
        let mut map = Map::new();
        let mut current_value: Value;
        let mut open_grammar_stack: Vec<Production> = Vec::new();
        loop {
            tracing::debug!("Next grammar id: {:?}", current_grammar_id);
            let current_prod = self
                .grammar
                .grs
                .grammar
                .iter()
                .find(|g| g.grammar_id == current_grammar_id.to_string())
                .unwrap();
            let bits_to_read = (current_prod.production.len() as f32).log2().ceil() as u8;

            let prod_index = self
                .bitstream_reader
                .read_n_bit_data(bits_to_read)
                .unwrap_or(0);
            //     open_grammar_stack.push(current_grammar_id);

            tracing::debug!("Bits to read: {} {}", bits_to_read, prod_index);
            if prod_index >= current_prod.production.len() as u8 {
                return Err(crate::exifier::Error::Generic(format!(
                    "Invalid production index. Expected 0-{}, got {}",
                    current_prod.production.len() - 1,
                    prod_index
                )));
            }
            let prod = &current_prod.production[prod_index as usize];

            // Extract values we need before making mutable calls to avoid borrowing conflicts
            let event = prod.event.clone();
            let start_element_grammar_id = prod.start_element_grammar_id;
            let start_element_namespace_id = prod.start_element_namespace_id;
            let start_element_local_name_id = prod.start_element_local_name_id;

            match event.as_str() {
                "startElement" => {
                    tracing::trace!("Start element: {:?}", event);

                    if let Some(namespace) = self
                        .grammar
                        .qnames
                        .namespace_context
                        .get(start_element_namespace_id.unwrap() as usize)
                    {
                        if let Some(local_name) = namespace
                            .qname_context
                            .iter()
                            .find(|q| q.local_name_id == start_element_local_name_id.unwrap())
                        {
                            if bits_to_read == 0 {
                                tracing::debug!("This start element was interpretted with 0 bits. Should this be pushed to the stack?");
                            } else {
                                open_grammar_stack.push(prod.clone());
                                tracing::debug!(
                                    "Pushing grammar id to stack: {}",
                                    current_grammar_id
                                );
                            }
                            opened_elements.push(local_name.local_name.clone());
                            tracing::debug!("Pushed element: {}", local_name.local_name.clone());
                        }
                    }

                    current_grammar_id = start_element_grammar_id.unwrap() as i32;

                    tracing::debug!(
                        "Prod event stack: {:#?}\n Open elements: {:#?}",
                        open_grammar_stack,
                        opened_elements
                    );
                }
                "endElement" => {
                    tracing::trace!("End element: {:?}", event);
                    tracing::trace!("Open elements: {:?}", opened_elements);
                    if bits_to_read == 0 {
                        tracing::trace!("This end element was interpretted with 0 bits. Should this be popped from the stack?");
                        tracing::trace!("Open elements: {:?} (Before pop)", opened_elements);
                    }

                    let next_in_queue = open_grammar_stack.pop();
                    current_grammar_id = next_in_queue.unwrap().next_grammar_id.unwrap() as i32;

                    let mut temp_map = Map::new();
                    temp_map.insert(
                        opened_elements.pop().unwrap(),
                        serde_json::Value::Object(map.clone()),
                    );
                    map = temp_map;

                    tracing::trace!(
                        "Prod event stack: {:#?}\n Open elements: {:#?}\n Current grammar id: {}\n Map: {:#?}",
                        open_grammar_stack,
                        opened_elements,
                        current_grammar_id,
                        map
                    );
                }
                "characters" => {
                    tracing::debug!("Characters:");

                    // current_grammar_id = prod.next_grammar_id.unwrap() as i32;
                    match opened_elements.pop() {
                        Some(number) if number == "number" => {
                            let number_type = self
                                .grammar
                                .get_simple_datatype(prod.characters_datatype_id.unwrap())
                                .unwrap()
                                .datatype
                                .clone();
                            tracing::trace!("Number type: {}", number_type);

                            let current_value: Value;

                            if number_type == "FLOAT" {
                                // let frac_data = self.read_signed_int()?;
                                // tracing::debug!("Read fraction: {}", frac_data);
                                // current_value = Value::Number(Number::from_f64((data as f64) + (frac_data as f64) / 100.0).unwrap());

                                let mantissa = self.read_signed_int()?;
                                let exponent = self.read_signed_int()?;

                                let fraction = mantissa as f64 / (1u64 << 52) as f64;
                                let reconstructed = (1.0 + fraction) * 2f64.powi(exponent as i32);

                                let read_number = reconstructed;

                                tracing::debug!(
                                    "Read exponent: {} {} {}",
                                    exponent,
                                    mantissa,
                                    read_number
                                );
                                current_value =
                                    Value::Number(Number::from_f64(read_number).unwrap());
                            } else {
                                let data = self.read_signed_int()?;
                                tracing::debug!("Read integer: {}", data);
                                current_value = Value::Number(Number::from(data as i64));
                            }

                            map.insert(opened_elements.pop().unwrap(), current_value.clone());
                            tracing::debug!("Current map: {:#?}", map);
                        }
                        Some(string) if string == "string" => {
                            let mut length = self.read_unsigned_int()?;
                            let mut encoded_string = Vec::new();
                            if length > 2 {
                                length -= 2;

                                for _i in 0..length {
                                    encoded_string.push(self.bitstream_reader.read_n_bit_data(8)?);
                                }
                                let decoded_string = str::from_utf8(&encoded_string).unwrap();
                                current_value = Value::String(String::from(decoded_string));
                                tracing::debug!("Read string: {}", decoded_string);
                                map.insert(opened_elements.pop().unwrap(), current_value.clone());
                            }
                            //break;
                        }
                        Some(unknown_element_type) => {
                            tracing::debug!("Unsupported type {:?}", unknown_element_type);
                        }
                        None => {
                            tracing::debug!("Stack empty");
                            break;
                        }
                    }

                    let next_in_queue = open_grammar_stack.pop();
                    current_grammar_id = next_in_queue.unwrap().next_grammar_id.unwrap() as i32;
                }
                "endDocument" => {
                    tracing::debug!("End document: {:?}", event);
                    break;
                }
                _ => {
                    tracing::warn!("Unknown event: {:?}", event);
                }
            }
        }
        //self.json_object.insert("map".to_string(), Value::Object(map));
        tracing::debug!("Final map: {:#?}", map);
        if let Some(Value::Object(obj)) = map.get("map") {
            self.json_object = obj.clone();
        } else {
            self.json_object = map;
        }
        Ok(())
    }

    fn read_signed_int(&mut self) -> Result<i128> {
        let sign = self.bitstream_reader.read_n_bit_data(1)?;
        tracing::trace!("Read sign: {}", sign);
        let mut data: i128 = self.read_unsigned_int()? as i128;
        if sign == 1 {
            // Negative numbers are stored as one less that their actual value.
            // So, on decode, add one to the magnitude and flip the sign.
            data += 1;
            data *= -1;
        }
        // *int_data = if data > i64::MAX as i128 {
        //     i64::MAX
        // } else if data < i64::MIN as i128 {
        //     i64::MIN
        // } else {
        //     data as i64
        // };
        Ok(data)
    }

    fn read_unsigned_int(&mut self) -> Result<u128> {
        let mut result: u128 = self.bitstream_reader.read_n_bit_data(8)? as u128;
        tracing::debug!("Read unsigned int: {:08b}", result);
        // < 128: just one byte, optimal case
        // ELSE: multiple bytes...
        if result >= 128 {
            result &= 127;
            let mut m_shift = 7;
            let mut b: u128;

            loop {
                // 1. Read the next octet
                b = self.bitstream_reader.read_n_bit_data(8)? as u128;
                tracing::debug!("Read unsigned int: {:08b}", b);
                // 2. Multiply the value of the unsigned number represented by
                // the 7 least significant
                // bits of the octet by the current multiplier and add the
                // result to the current value.
                result += ((b & 127) << m_shift) as u128;
                tracing::debug!("Result is now: {:0b}", result);
                // 3. Multiply the multiplier by 128
                m_shift += 7;
                // 4. If the most significant bit of the octet was 1, go back to
                // step 1

                if b < 128 || m_shift > 128 {
                    break;
                }
            }
        }

        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_read_unsigned_int_127() {
        let mut decoder = ExiDecoder::new();
        decoder.bitstream_reader = BitStreamReader::new(vec![0b01111111]);
        let result = decoder.read_unsigned_int();
        assert_eq!(result, Ok(127));
    }

    #[test]
    fn test_read_unsigned_int_128() {
        let mut decoder = ExiDecoder::new();
        decoder.bitstream_reader = BitStreamReader::new(vec![0b10000000, 0b00000001]);
        let result = decoder.read_unsigned_int();
        assert_eq!(result, Ok(128));
    }

    #[test]
    fn test_read_unsigned_int_256() {
        let mut decoder = ExiDecoder::new();
        decoder.bitstream_reader = BitStreamReader::new(vec![0b10000000, 0b00000010]);
        let result = decoder.read_unsigned_int();
        assert_eq!(result, Ok(256));
    }

    #[test]
    fn test_write_unsigned_int_512() {
        let mut decoder = ExiDecoder::new();
        decoder.bitstream_reader = BitStreamReader::new(vec![0b10000000, 0b00000100]);
        let result = decoder.read_unsigned_int();
        assert_eq!(result, Ok(512));
    }
}
