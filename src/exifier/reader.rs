use crate::exifier::prelude::*;

#[derive(Debug)]
pub struct BitStreamReader {
    buffer: Vec<u8>,
    /// Next position of 0 means the last byte is full or we are on the first byte. Next data will be written on a new byte.
    next_bit_position: u8,
}

impl Default for BitStreamReader {
    fn default() -> Self {
        BitStreamReader {
            buffer: Vec::new(),
            next_bit_position: 0,
        }
    }
}

impl BitStreamReader {
    pub fn new(buffer: Vec<u8>) -> Self {
        let binary_str: Vec<String> = buffer.iter().map(|b| format!("{:08b}", b)).collect();
        tracing::debug!("{}", binary_str.join(" "));

        BitStreamReader {
            buffer,
            next_bit_position: 0,
        }
    }

    pub fn read_n_bit_data(&mut self, num_bits: u8) -> Result<u8> {
        if self.buffer.is_empty() {
            tracing::warn!("Tried to read from empty buffer...");
            return Err(crate::exifier::Error::Generic(format!(
                "No more data to read."
            )));
        }
        if num_bits == 0 {
            tracing::trace!("Tried to read 0 bits");
            return Ok(0);
        }

        if num_bits > 8 {
            tracing::warn!(
                "Can't read more than a byte with read_n_bit_data. Tried to read {} bits",
                num_bits
            );
            return Ok(0);
        }

        if self.next_bit_position + num_bits <= 8 {
            self.read_bits_from_stream(num_bits)
        } else {
            let num_bits_first_byte = 8 - self.next_bit_position;
            let num_bits_second_byte = num_bits - num_bits_first_byte;

            let data_in_first_byte = self.read_bits_from_stream(num_bits_first_byte)?;
            tracing::trace!(
                "Read {} bits from first byte: {:#?}",
                num_bits_first_byte,
                data_in_first_byte
            );
            let data_in_second_byte = self.read_bits_from_stream(num_bits_second_byte)?;
            tracing::trace!(
                "Read {} bits from second byte: {:#?}",
                num_bits_second_byte,
                data_in_second_byte
            );

            Ok((data_in_first_byte << num_bits_second_byte) | data_in_second_byte)
        }
    }

    fn read_bits_from_stream(&mut self, num_bits: u8) -> Result<u8> {
        if self.buffer.is_empty() {
            tracing::warn!("Tried to read from empty buffer");
            return Err(crate::exifier::Error::Generic(format!(
                "No more data to read."
            )));
        }

        let next_byte = self.buffer[0].clone();

        let mask = ((1u16 << num_bits) - 1) as u8;

        let left_aligned = next_byte >> (8 - (self.next_bit_position + num_bits));
        let masked = left_aligned & mask;

        self.next_bit_position += num_bits;
        if self.next_bit_position > 7 {
            self.next_bit_position = 0;
            self.buffer.remove(0);
        }

        Ok(masked)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_new_bitstream_reader() {
        let reader = BitStreamReader::new(vec![0b10101010]);
        assert_eq!(reader.buffer.len(), 1);
        assert_eq!(reader.next_bit_position, 0);
    }

    #[test]
    fn test_read_single_bit() {
        let mut reader = BitStreamReader::new(vec![0b10101010]);
        assert_eq!(reader.read_n_bit_data(1), Ok(0b1));
        assert_eq!(reader.next_bit_position, 1);
    }

    #[test]
    fn test_read_multiple_bits() {
        let mut reader = BitStreamReader::new(vec![0b10101010]);
        assert_eq!(reader.read_n_bit_data(3), Ok(0b101));
        assert_eq!(reader.next_bit_position, 3);
    }

    #[test]
    fn test_read_full_byte() {
        let mut reader = BitStreamReader::new(vec![0b10101010]);
        assert_eq!(reader.read_n_bit_data(8), Ok(0b10101010));
        assert_eq!(reader.next_bit_position, 0);
    }

    #[test]
    fn test_read_across_byte_boundary() {
        let mut reader = BitStreamReader::new(vec![0b11101000, 0b01000000]);
        assert_eq!(reader.read_n_bit_data(6), Ok(0b111010));
        assert_eq!(reader.next_bit_position, 6);
    }

    #[test]
    fn test_two_consecutive_reads() {
        let mut reader = BitStreamReader::new(vec![0b11101000, 0b01000000]);
        assert_eq!(reader.read_n_bit_data(6), Ok(0b111010));
        assert_eq!(reader.next_bit_position, 6);
        assert_eq!(reader.read_n_bit_data(4), Ok(0b0001));
        assert_eq!(reader.next_bit_position, 2);
    }

    #[test]
    fn test_three_consecutive_reads() {
        let mut reader = BitStreamReader::new(vec![0b11101000, 0b01000001]);
        assert_eq!(reader.read_n_bit_data(6), Ok(0b111010));
        assert_eq!(reader.next_bit_position, 6);
        assert_eq!(reader.read_n_bit_data(4), Ok(0b0001));
        assert_eq!(reader.next_bit_position, 2);
        assert_eq!(reader.read_n_bit_data(6), Ok(0b000001));
        assert_eq!(reader.next_bit_position, 0);
    }
}
