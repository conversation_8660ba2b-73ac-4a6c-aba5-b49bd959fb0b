use crate::exifier::exi_decoder::ExiDecoder;
use crate::exifier::exi_encoder::ExiEncoder;
use crate::exifier::grammar::EXIGrammar;
use crate::exifier::prelude::*;
use serde_json::Value;

#[derive(Debug)]
pub struct Exifier {
    encoder: ExiEncoder,
    decoder: ExiDecoder,
}

impl Default for Exifier {
    fn default() -> Self {
        let codec = Exifier {
            encoder: ExiEncoder::new(),
            decoder: ExiDecoder::new(),
        };
        println!(
            "Codec version:{}",
            codec
                .get_version()
                .unwrap_or(String::from("Failed to get version"))
        );
        codec
    }
}

impl Exifier {
    pub fn encode(&mut self, json_value: Value) -> Result<Vec<u8>> {
        self.encoder.encode(json_value)
    }

    pub fn load_grammar(&mut self, exi_grammar: EXIGrammar) -> Result<()> {
        self.encoder.load_grammar(exi_grammar.clone())?;
        self.decoder.load_grammar(exi_grammar.clone())?;
        Ok(())
    }

    pub fn decode(&mut self, encoded_bytes: Vec<u8>) -> Result<Value> {
        self.decoder.decode(encoded_bytes)
    }

    pub fn get_version(&self) -> Result<String> {
        Ok(env!("CARGO_PKG_VERSION").to_string())
    }
}


