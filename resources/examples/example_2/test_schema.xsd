<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="map">
        <xs:complexType>
            <xs:all>
                <xs:element minOccurs="0" name="firstname">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="string" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="age">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="number" type="xs:double"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="lastname">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="string" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>
</xs:schema>
